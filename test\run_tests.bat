@echo off
echo === 隧道系统集成测试 ===
echo.

REM 检查当前目录
if not exist "tunnel_test.go" (
    echo 错误: 请在 test 目录下运行此脚本
    exit /b 1
)

echo --- 运行基础集成测试 ---
echo 描述: 测试配置加载、数据库连接、客户端注册等基础功能
echo 命令: go test -v -run TestTunnelIntegration
echo.
go test -v -run TestTunnelIntegration
if %errorlevel% equ 0 (
    echo ✅ 基础集成测试通过
) else (
    echo ❌ 基础集成测试失败
)
echo.

echo --- 运行客户端服务端集成测试 ---
echo 描述: 测试客户端和服务端配置文件集成
echo 命令: go test -v -run TestClientServerIntegration
echo.
go test -v -run TestClientServerIntegration
if %errorlevel% equ 0 (
    echo ✅ 客户端服务端集成测试通过
) else (
    echo ❌ 客户端服务端集成测试失败
)
echo.

echo --- 运行所有基础测试 ---
echo 描述: 运行所有不需要 CGO 的测试
echo 命令: go test -v
echo.
go test -v
if %errorlevel% equ 0 (
    echo ✅ 所有基础测试通过
) else (
    echo ❌ 部分基础测试失败
)
echo.

echo --- 检查 CGO 和 SQLite3 支持 ---
echo 正在检查 CGO 支持...
set CGO_ENABLED=1
go version > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Go 环境可用
    echo ⚠️  SQLite3 测试需要 GCC 编译器，如需运行请手动执行:
    echo    set CGO_ENABLED=1
    echo    go test -v -run TestTunnelIntegrationWithSQLite -tags cgo
) else (
    echo ❌ Go 环境不可用
)
echo.

echo === 测试完成 ===
echo.
echo 测试说明:
echo - 基础测试使用内存数据库，无需外部依赖
echo - SQLite3 测试需要 CGO 支持和 GCC 编译器
echo - 所有测试使用不同端口范围避免冲突
echo.
echo 如需查看详细说明，请参考 README.md 文件
pause
