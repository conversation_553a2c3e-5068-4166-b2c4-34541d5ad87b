# 隧道系统测试运行脚本
# PowerShell 脚本用于运行各种测试

Write-Host "=== 隧道系统集成测试 ===" -ForegroundColor Green
Write-Host ""

# 检查当前目录
if (!(Test-Path "tunnel_test.go")) {
    Write-Host "错误: 请在 test 目录下运行此脚本" -ForegroundColor Red
    exit 1
}

# 函数：运行测试并显示结果
function Run-Test {
    param(
        [string]$TestName,
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "--- $TestName ---" -ForegroundColor Yellow
    Write-Host "描述: $Description"
    Write-Host "命令: $Command"
    Write-Host ""
    
    $startTime = Get-Date
    Invoke-Expression $Command
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $TestName 通过 (耗时: $($duration.TotalSeconds.ToString('F2'))秒)" -ForegroundColor Green
    } else {
        Write-Host "❌ $TestName 失败 (耗时: $($duration.TotalSeconds.ToString('F2'))秒)" -ForegroundColor Red
    }
    Write-Host ""
}

# 1. 运行基础集成测试
Run-Test -TestName "基础集成测试" -Command "go test -v -run TestTunnelIntegration" -Description "测试配置加载、数据库连接、客户端注册等基础功能"

# 2. 运行客户端服务端集成测试
Run-Test -TestName "客户端服务端集成测试" -Command "go test -v -run TestClientServerIntegration" -Description "测试客户端和服务端配置文件集成"

# 3. 运行所有基础测试
Run-Test -TestName "所有基础测试" -Command "go test -v" -Description "运行所有不需要 CGO 的测试"

# 4. 检查是否支持 CGO 和 SQLite3
Write-Host "--- 检查 CGO 和 SQLite3 支持 ---" -ForegroundColor Yellow
Write-Host "正在检查 CGO 支持..."

$env:CGO_ENABLED = "1"
$cgoTest = go build -o nul 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ CGO 支持可用" -ForegroundColor Green
    
    # 尝试运行 SQLite3 测试
    Write-Host "正在尝试运行 SQLite3 测试..."
    Run-Test -TestName "SQLite3 集成测试" -Command '$env:CGO_ENABLED="1"; go test -v -run TestTunnelIntegrationWithSQLite -tags cgo' -Description "测试 SQLite3 数据库功能"
} else {
    Write-Host "⚠️  CGO 支持不可用，跳过 SQLite3 测试" -ForegroundColor Yellow
    Write-Host "如需运行 SQLite3 测试，请安装 GCC 编译器" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "测试说明:"
Write-Host "- 基础测试使用内存数据库，无需外部依赖"
Write-Host "- SQLite3 测试需要 CGO 支持和 GCC 编译器"
Write-Host "- 所有测试使用不同端口范围避免冲突"
Write-Host ""
Write-Host "如需查看详细说明，请参考 README.md 文件"
