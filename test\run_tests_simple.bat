@echo off
echo === Tunnel System Integration Tests ===
echo.

REM Check current directory
if not exist "tunnel_test.go" (
    echo Error: Please run this script in the test directory
    exit /b 1
)

echo --- Running Basic Integration Tests ---
echo Description: Test config loading, database connection, client registration
echo Command: go test -v -run TestTunnelIntegration
echo.
go test -v -run TestTunnelIntegration
if %errorlevel% equ 0 (
    echo SUCCESS: Basic integration tests passed
) else (
    echo FAILED: Basic integration tests failed
)
echo.

echo --- Running Client-Server Integration Tests ---
echo Description: Test client and server config file integration
echo Command: go test -v -run TestClientServerIntegration
echo.
go test -v -run TestClientServerIntegration
if %errorlevel% equ 0 (
    echo SUCCESS: Client-Server integration tests passed
) else (
    echo FAILED: Client-Server integration tests failed
)
echo.

echo --- Running All Basic Tests ---
echo Description: Run all tests that don't require CGO
echo Command: go test -v
echo.
go test -v
if %errorlevel% equ 0 (
    echo SUCCESS: All basic tests passed
) else (
    echo FAILED: Some basic tests failed
)
echo.

echo --- Checking CGO and SQLite3 Support ---
echo Checking CGO support...
set CGO_ENABLED=1
go version > nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Go environment available
    echo WARNING: SQLite3 tests require GCC compiler, to run manually execute:
    echo    set CGO_ENABLED=1
    echo    go test -v -run TestTunnelIntegrationWithSQLite -tags cgo
) else (
    echo FAILED: Go environment not available
)
echo.

echo === Tests Completed ===
echo.
echo Test Notes:
echo - Basic tests use in-memory database, no external dependencies required
echo - SQLite3 tests require CGO support and GCC compiler
echo - All tests use different port ranges to avoid conflicts
echo.
echo For detailed instructions, please refer to README.md file
pause
