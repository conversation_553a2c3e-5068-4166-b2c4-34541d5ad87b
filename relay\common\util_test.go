package common

import (
	"os"
	"runtime"
	"testing"
)

func TestSetAndGetEnvironmentVariable(t *testing.T) {
	testKey := "TEST_RELAY_ENV_VAR"
	testValue := "test_value_123"

	// 清理测试环境
	defer func() {
		if runtime.GOOS == "linux" {
			// 在Linux上，删除测试文件
			os.Remove(LinuxEnvFilePath)
		}
	}()

	// 测试设置环境变量
	err := SetEnvironmentVariable(testKey, testValue)
	if err != nil {
		t.Fatalf("Failed to set environment variable: %v", err)
	}

	// 测试获取环境变量
	retrievedValue, err := GetEnvironmentVariable(testKey)
	if err != nil {
		t.Fatalf("Failed to get environment variable: %v", err)
	}

	if retrievedValue != testValue {
		t.Errorf("Expected value %s, got %s", testValue, retrievedValue)
	}
}

func TestClientVersionFunctions(t *testing.T) {
	testVersion := "1.2.3-test"

	// 清理测试环境
	defer func() {
		if runtime.GOOS == "linux" {
			// 在Linux上，删除测试文件
			os.Remove(LinuxEnvFilePath)
		}
	}()

	// 测试设置客户端版本
	err := SetClientVersion(testVersion)
	if err != nil {
		t.Fatalf("Failed to set client version: %v", err)
	}

	// 测试获取客户端版本
	retrievedVersion := GetClientVersion()

	if retrievedVersion != testVersion {
		t.Errorf("Expected version %s, got %s", testVersion, retrievedVersion)
	}
}

func TestGetNonExistentEnvironmentVariable(t *testing.T) {
	nonExistentKey := "NON_EXISTENT_TEST_KEY_12345"

	// 测试获取不存在的环境变量
	_, err := GetEnvironmentVariable(nonExistentKey)
	if err == nil {
		t.Error("Expected error when getting non-existent environment variable")
	}
}

func TestSetEnvironmentVariableWithSpecialCharacters(t *testing.T) {
	testKey := "TEST_SPECIAL_CHARS"
	testValue := "value with spaces and \"quotes\" and 'single quotes'"

	// 清理测试环境
	defer func() {
		if runtime.GOOS == "linux" {
			// 在Linux上，删除测试文件
			os.Remove(LinuxEnvFilePath)
		}
	}()

	// 测试设置包含特殊字符的环境变量
	err := SetEnvironmentVariable(testKey, testValue)
	if err != nil {
		t.Fatalf("Failed to set environment variable with special characters: %v", err)
	}

	// 测试获取环境变量
	retrievedValue, err := GetEnvironmentVariable(testKey)
	if err != nil {
		t.Fatalf("Failed to get environment variable with special characters: %v", err)
	}

	if retrievedValue != testValue {
		t.Errorf("Expected value %s, got %s", testValue, retrievedValue)
	}
}

func TestLinuxEnvFileOperations(t *testing.T) {
	if runtime.GOOS != "linux" {
		t.Skip("This test is only for Linux")
	}

	// 清理测试环境
	defer os.Remove(LinuxEnvFilePath)

	// 测试写入多个环境变量
	testVars := map[string]string{
		"VAR1": "value1",
		"VAR2": "value with spaces",
		"VAR3": "value_with_underscores",
	}

	for key, value := range testVars {
		err := SetEnvironmentVariable(key, value)
		if err != nil {
			t.Fatalf("Failed to set environment variable %s: %v", key, err)
		}
	}

	// 验证所有变量都能正确读取
	for key, expectedValue := range testVars {
		retrievedValue, err := GetEnvironmentVariable(key)
		if err != nil {
			t.Fatalf("Failed to get environment variable %s: %v", key, err)
		}

		if retrievedValue != expectedValue {
			t.Errorf("For key %s, expected value %s, got %s", key, expectedValue, retrievedValue)
		}
	}
}

func TestWindowsSystemEnvOperations(t *testing.T) {
	if runtime.GOOS != "windows" {
		t.Skip("This test is only for Windows")
	}

	testKey := "TEST_WINDOWS_ENV_VAR"
	testValue := "windows_test_value"

	// 测试设置Windows系统环境变量
	err := SetEnvironmentVariable(testKey, testValue)
	if err != nil {
		t.Fatalf("Failed to set Windows environment variable: %v", err)
	}

	// 测试获取Windows系统环境变量
	retrievedValue, err := GetEnvironmentVariable(testKey)
	if err != nil {
		t.Fatalf("Failed to get Windows environment variable: %v", err)
	}

	if retrievedValue != testValue {
		t.Errorf("Expected value %s, got %s", testValue, retrievedValue)
	}
}
