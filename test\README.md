# 隧道系统集成测试

本目录包含了隧道系统的集成测试，用于验证服务端和客户端的各项功能。

## 测试文件说明

### tunnel_test.go
主要的集成测试文件，包含以下测试：

1. **TestTunnelIntegration** - 主要集成测试
   - ConfigLoading: 配置文件加载测试
   - DatabaseConnection: 数据库连接测试（使用内存模式）
   - ClientRegistration: 客户端注册测试
   - PortAllocation: 端口分配测试
   - URLRegistration: URL注册测试
   - TunnelRefresh: 隧道刷新测试
   - ClientFiltering: 客户端过滤测试

2. **TestClientServerIntegration** - 客户端服务端集成测试
   - 验证配置文件创建和加载
   - 验证客户端和服务端配置一致性

### tunnel_sqlite_test.go
SQLite3 数据库专用测试文件（需要 CGO 支持）：

1. **TestTunnelIntegrationWithSQLite** - SQLite 集成测试
   - SQLiteConfigLoading: SQLite 配置加载测试
   - SQLiteDatabaseConnection: SQLite 数据库连接测试
   - SQLiteDataPersistence: SQLite 数据持久化测试

## 运行测试

### 基础测试（无需 CGO）
```bash
cd test
go test -v
```

### SQLite3 测试（需要 CGO 和 GCC）
```bash
cd test
CGO_ENABLED=1 go test -v -tags cgo
```

### 运行特定测试
```bash
# 只运行主集成测试
go test -v -run TestTunnelIntegration

# 只运行客户端服务端集成测试
go test -v -run TestClientServerIntegration

# 只运行 SQLite 测试（需要 CGO）
CGO_ENABLED=1 go test -v -run TestTunnelIntegrationWithSQLite -tags cgo
```

## 测试配置

### 端口配置
测试使用以下端口范围以避免冲突：

- **TestTunnelIntegration**: 28080-29100
- **TestClientServerIntegration**: 38080-39100  
- **TestTunnelIntegrationWithSQLite**: 48080-49100

### 数据库配置
- **基础测试**: 使用 `DbType: "none"` 禁用数据库
- **SQLite 测试**: 使用临时 SQLite 数据库文件

## 测试覆盖的功能

### 服务端功能
- [x] 配置文件加载
- [x] 数据库连接（SQLite3/内存模式）
- [x] HTTP API 服务器启动
- [x] TCP 代理服务器启动
- [x] 客户端注册处理
- [x] 端口分配
- [x] URL 映射注册
- [x] 隧道刷新
- [x] 客户端过滤和分组

### 客户端功能
- [x] 配置文件加载和解析
- [x] 服务端连接
- [x] 隧道配置验证

### 数据库功能（SQLite3）
- [x] 数据库文件创建
- [x] 表结构自动迁移
- [x] 数据持久化验证

## 依赖要求

### 基础测试
- Go 1.24.2+
- 无需外部依赖

### SQLite3 测试
- Go 1.24.2+
- CGO 支持
- GCC 编译器
- SQLite3 开发库

## 故障排除

### CGO 相关错误
如果遇到 `cgo: C compiler "gcc" not found` 错误：

1. **Windows**: 安装 TDM-GCC 或 MinGW-w64
2. **Linux**: 安装 `build-essential` 包
3. **macOS**: 安装 Xcode Command Line Tools

### 端口冲突
如果遇到端口占用错误，请检查：
1. 是否有其他测试实例在运行
2. 是否有其他服务占用了测试端口
3. 可以修改测试文件中的端口配置

### 数据库连接错误
如果遇到数据库连接问题：
1. 检查临时目录权限
2. 确保 SQLite3 驱动正确安装
3. 验证 CGO 是否正确启用

## 测试结果示例

成功运行的测试输出示例：
```
=== RUN   TestTunnelIntegration
=== RUN   TestTunnelIntegration/ConfigLoading
    tunnel_test.go:196: Testing configuration loading...
    tunnel_test.go:216: Configuration loading test passed
=== RUN   TestTunnelIntegration/DatabaseConnection
    tunnel_test.go:221: Testing database connection...
    tunnel_test.go:235: Database connection is nil (database disabled), test passed
...
--- PASS: TestTunnelIntegration (2.01s)
--- PASS: TestClientServerIntegration (2.00s)
PASS
ok      socks/test      4.266s
```
