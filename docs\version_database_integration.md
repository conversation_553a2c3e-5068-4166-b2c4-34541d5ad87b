# 版本信息数据库集成

## 概述

本文档描述了在创建新的端口映射和URL映射时，将客户端版本号和relay版本号写入数据库的实现。

## 数据库表结构修改

### 新增字段

在 `gateway_intranet_tunnel` 表中新增了两个版本字段：

```sql
ALTER TABLE gateway_intranet_tunnel 
ADD COLUMN relay_version VARCHAR(50),
ADD COLUMN client_version VARCHAR(50);
```

### 数据库实体更新

**文件**: `server/infra/repo/intranet_tunnel_repo.go`

```go
type IntranetTunnel struct {
    // ... 现有字段 ...
    RelayVersion  string `gorm:"column:relay_version"`  // Relay版本
    ClientVersion string `gorm:"column:client_version"` // 客户端版本
}
```

## 数据转换函数更新

### 端口映射转换

**文件**: `server/domain/repo/dto.go`

#### PortMapping -> IntranetTunnel
```go
func PortMapping2IntranetTunnel(mapping *entity.PortMapping) *repo.IntranetTunnel {
    return &repo.IntranetTunnel{
        // ... 现有字段 ...
        RelayVersion:  mapping.Client.RelayVersion,
        ClientVersion: mapping.Client.ClientVersion,
    }
}
```

#### IntranetTunnel -> PortMapping
```go
func IntranetTunnel2PortMapping(it *repo.IntranetTunnel) *entity.PortMapping {
    return &entity.PortMapping{
        // ... 现有字段 ...
        Client: &entity.Client{
            // ... 现有字段 ...
            RelayVersion:  it.RelayVersion,
            ClientVersion: it.ClientVersion,
        },
    }
}
```

### URL映射转换

#### URLMapping -> IntranetTunnel
```go
func URLMapping2IntranetTunnel(mapping *entity.URLMapping) *repo.IntranetTunnel {
    return &repo.IntranetTunnel{
        // ... 现有字段 ...
        RelayVersion:  mapping.Client.RelayVersion,
        ClientVersion: mapping.Client.ClientVersion,
    }
}
```

#### IntranetTunnel -> URLMapping
```go
func IntranetTunnel2URLMapping(it *repo.IntranetTunnel) *entity.URLMapping {
    return &entity.URLMapping{
        // ... 现有字段 ...
        Client: &entity.Client{
            // ... 现有字段 ...
            RelayVersion:  it.RelayVersion,
            ClientVersion: it.ClientVersion,
        },
    }
}
```

## 业务逻辑更新

### 端口映射创建

**文件**: `server/domain/service/proxy.go`

在 `AllocateAvailablePort` 方法中，创建端口映射时包含版本信息：

```go
p.connection.AddMapping(clientUUID, ipPort, &entity.PortMapping{
    Name: client.GetTunnelName(ipPort),
    Client: &entity.Client{
        Name:          client.Name,
        IP:            client.IP,
        UUID:          client.UUID,
        Type:          "TCP",
        Group:         client.Group,
        RelayVersion:  client.RelayVersion,  // 新增
        ClientVersion: client.ClientVersion, // 新增
    },
    // ... 其他字段 ...
})
```

### URL映射创建

**文件**: `server/domain/service/url_proxy.go`

在 `RegisterURLMapping` 方法中，创建URL映射时包含版本信息：

```go
mapping := &entity.URLMapping{
    Name: appName,
    Client: &entity.Client{
        Name:          client.Name,
        IP:            client.IP,
        UUID:          client.UUID,
        Type:          apiType,
        Group:         client.Group,
        RelayVersion:  client.RelayVersion,  // 新增
        ClientVersion: client.ClientVersion, // 新增
    },
    // ... 其他字段 ...
}
```

## 数据流程

### 端口映射创建流程

1. **客户端注册**: 客户端向服务端注册时上报版本信息
2. **版本存储**: 服务端将版本信息存储在 `Client` 实体中
3. **端口分配**: 调用 `AllocateAvailablePort` 创建端口映射
4. **映射创建**: 创建 `PortMapping` 实体，包含完整的客户端信息（含版本）
5. **数据库写入**: 通过 `PortMapping2IntranetTunnel` 转换并写入数据库
6. **版本持久化**: 版本信息被保存到 `relay_version` 和 `client_version` 字段

### URL映射创建流程

1. **客户端注册**: 客户端向服务端注册时上报版本信息
2. **版本存储**: 服务端将版本信息存储在 `Client` 实体中
3. **URL注册**: 调用 `RegisterURLMapping` 创建URL映射
4. **映射创建**: 创建 `URLMapping` 实体，包含完整的客户端信息（含版本）
5. **数据库写入**: 通过 `URLMapping2IntranetTunnel` 转换并写入数据库
6. **版本持久化**: 版本信息被保存到 `relay_version` 和 `client_version` 字段

## 版本信息来源

### Relay版本
- **来源**: `relay.Version` 常量
- **格式**: 数字字符串（如 "1"）
- **用途**: 协议兼容性检查

### 客户端版本
- **来源**: 环境变量 `BOS_Client_Version`
- **格式**: 语义化版本字符串（如 "1.2.3-beta"）
- **用途**: 功能兼容性和调试

## 数据库查询示例

### 按版本筛选映射

```sql
-- 查询特定Relay版本的映射
SELECT * FROM gateway_intranet_tunnel 
WHERE relay_version = '1';

-- 查询特定客户端版本的映射
SELECT * FROM gateway_intranet_tunnel 
WHERE client_version = '1.2.3-beta';

-- 查询版本组合
SELECT * FROM gateway_intranet_tunnel 
WHERE relay_version = '1' AND client_version LIKE '1.2.%';
```

### 版本统计

```sql
-- 统计各Relay版本的映射数量
SELECT relay_version, COUNT(*) as mapping_count 
FROM gateway_intranet_tunnel 
GROUP BY relay_version;

-- 统计各客户端版本的映射数量
SELECT client_version, COUNT(*) as mapping_count 
FROM gateway_intranet_tunnel 
GROUP BY client_version;
```

## 测试验证

创建了测试脚本 `server/test_version_db.go` 用于验证版本信息的数据库集成：

1. **数据库迁移测试**: 验证新字段的创建
2. **端口映射测试**: 验证端口映射的版本信息存储
3. **URL映射测试**: 验证URL映射的版本信息存储
4. **数据读取测试**: 验证从数据库读取版本信息
5. **数据转换测试**: 验证实体与数据库记录的双向转换

## 兼容性说明

### 向后兼容
- 新增字段允许为空，不影响现有数据
- 现有映射记录的版本字段将为空字符串
- 系统会继续正常工作，只是缺少版本信息

### 数据迁移
- 使用 GORM 的 `AutoMigrate` 功能自动添加新字段
- 无需手动执行 SQL 迁移脚本
- 新字段会自动添加到现有表中

## 监控和维护

### 版本信息监控
- 可通过数据库查询监控各版本的使用情况
- 支持版本分布统计和趋势分析
- 便于制定客户端升级策略

### 数据清理
- 版本信息随映射记录一起管理
- 映射删除时版本信息也会被清理
- 无需额外的清理逻辑

## 总结

通过本次修改，系统现在能够：

1. **完整记录版本信息**: 在创建端口映射和URL映射时，自动将客户端的Relay版本和客户端版本写入数据库
2. **支持版本查询**: 可以通过数据库查询特定版本的映射记录
3. **保持数据一致性**: 版本信息在内存实体和数据库记录之间保持同步
4. **向后兼容**: 不影响现有功能，平滑升级

这为后续的版本管理、兼容性检查和系统监控提供了重要的数据基础。
